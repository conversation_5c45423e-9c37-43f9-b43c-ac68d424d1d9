import * as React from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Heart, Download } from 'lucide-react-native';

const { width } = Dimensions.get('window');

interface WallpaperCardProps {
  imageUrl: string;
  onPress?: () => void;
  onFavorite?: () => void;
  onDownload?: () => void;
  isFavorited?: boolean;
}

export default function WallpaperCard({
  imageUrl,
  onPress,
  onFavorite,
  onDownload,
  isFavorited = false,
}: WallpaperCardProps) {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.overlay}>
        <TouchableOpacity style={styles.actionButton} onPress={onFavorite}>
          <Heart 
            color="#fff" 
            size={16} 
            fill={isFavorited ? '#fff' : 'none'} 
          />
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={onDownload}>
          <Download color="#fff" size={16} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    width: (width - 60) / 2,
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
    marginBottom: 12,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 12,
    right: 12,
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});