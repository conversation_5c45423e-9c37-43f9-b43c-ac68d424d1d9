export interface Wallpaper {
  id: string;
  url: string;
  thumbnailUrl: string;
  title: string;
  category: string;
  tags: string[];
  resolution: string;
  downloads: number;
  likes: number;
  createdAt: string;
}

export interface Category {
  id: string;
  name: string;
  count: number;
  gradient: string[];
}

export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  favorites: string[];
  downloads: string[];
}