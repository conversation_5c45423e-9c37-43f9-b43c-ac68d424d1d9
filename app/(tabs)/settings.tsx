import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  User,
  Download,
  Smartphone,
  Bell,
  HardDrive,
  Info,
  LogOut,
  ChevronRight,
} from 'lucide-react-native';

const settingsData = [
  {
    title: 'Profile',
    items: [
      { name: 'Edit Profile', icon: User, hasSwitch: false },
      { name: 'Account Settings', icon: User, hasSwitch: false },
    ],
  },
  {
    title: 'Preferences',
    items: [
      { name: 'Download Quality', icon: Download, hasSwitch: false, subtitle: 'High (HD)' },
      { name: 'Auto Wallpaper Change', icon: Smartphone, hasSwitch: true, value: false },
      { name: 'Push Notifications', icon: Bell, hasSwitch: true, value: true },
    ],
  },
  {
    title: 'Storage',
    items: [
      { name: 'Manage Downloads', icon: HardDrive, hasSwitch: false, subtitle: '2.4 GB used' },
      { name: 'Clear Cache', icon: HardDrive, hasSwitch: false },
    ],
  },
  {
    title: 'About',
    items: [
      { name: 'Help & Support', icon: Info, hasSwitch: false },
      { name: 'Privacy Policy', icon: Info, hasSwitch: false },
      { name: 'Terms of Service', icon: Info, hasSwitch: false },
    ],
  },
];

export default function SettingsScreen() {
  const [switches, setSwitches] = React.useState<{ [key: string]: boolean }>({
    'Auto Wallpaper Change': false,
    'Push Notifications': true,
  });

  const toggleSwitch = (name: string) => {
    setSwitches(prev => ({
      ...prev,
      [name]: !prev[name],
    }));
  };

  const handleLogout = () => {
    // Handle logout logic
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Card */}
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.profileCard}
        >
          <View style={styles.profileInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>JD</Text>
            </View>
            <View style={styles.profileDetails}>
              <Text style={styles.profileName}>John Doe</Text>
              <Text style={styles.profileEmail}><EMAIL></Text>
            </View>
            <TouchableOpacity>
              <ChevronRight color="#fff" size={20} />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Settings Sections */}
        {settingsData.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <View style={styles.sectionContent}>
              {section.items.map((item, itemIndex) => (
                <TouchableOpacity key={itemIndex} style={styles.settingItem}>
                  <View style={styles.settingLeft}>
                    <View style={styles.settingIcon}>
                      <item.icon color="#667eea" size={20} />
                    </View>
                    <View style={styles.settingText}>
                      <Text style={styles.settingName}>{item.name}</Text>
                      {item.subtitle && (
                        <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
                      )}
                    </View>
                  </View>
                  <View style={styles.settingRight}>
                    {item.hasSwitch ? (
                      <Switch
                        value={switches[item.name]}
                        onValueChange={() => toggleSwitch(item.name)}
                        trackColor={{ false: '#e9ecef', true: '#667eea' }}
                        thumbColor="#fff"
                      />
                    ) : (
                      <ChevronRight color="#999" size={16} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <LogOut color="#FF6B6B" size={20} />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  profileCard: {
    margin: 24,
    borderRadius: 16,
    padding: 20,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileEmail: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    paddingHorizontal: 24,
    marginBottom: 12,
  },
  sectionContent: {
    backgroundColor: '#fff',
    marginHorizontal: 24,
    borderRadius: 16,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 12,
    color: '#666',
  },
  settingRight: {
    marginLeft: 16,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 16,
    marginBottom: 24,
    gap: 12,
  },
  logoutText: {
    color: '#FF6B6B',
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  footerText: {
    color: '#999',
    fontSize: 12,
  },
});