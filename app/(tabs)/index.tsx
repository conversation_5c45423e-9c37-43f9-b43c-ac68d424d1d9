import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { <PERSON><PERSON>, Bell, ChevronLeft, ChevronRight, Heart } from 'lucide-react-native';

const { width } = Dimensions.get('window');
const cardWidth = width - 48;

const featuredWallpapers = [
  {
    id: 1,
    url: 'https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=800',
    title: 'Ocean Wave',
  },
  {
    id: 2,
    url: 'https://images.pexels.com/photos/1366930/pexels-photo-1366930.jpeg?auto=compress&cs=tinysrgb&w=800',
    title: 'Mountain View',
  },
  {
    id: 3,
    url: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800',
    title: 'Forest Path',
  },
];

const categories = [
  { name: 'Nature', count: '2.1k', color: '#4CAF50' },
  { name: 'Abstract', count: '1.8k', color: '#9C27B0' },
  { name: 'Minimal', count: '1.2k', color: '#607D8B' },
  { name: 'Space', count: '856', color: '#3F51B5' },
];

const trendingWallpapers = [
  'https://images.pexels.com/photos/1287145/pexels-photo-1287145.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1323550/pexels-photo-1323550.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1366957/pexels-photo-1366957.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1671324/pexels-photo-1671324.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1624496/pexels-photo-1624496.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1629236/pexels-photo-1629236.jpeg?auto=compress&cs=tinysrgb&w=400',
];

export default function HomeScreen() {
  const [currentFeatured, setCurrentFeatured] = useState(0);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.menuButton}>
          <Menu color="#333" size={24} />
        </TouchableOpacity>
        <View style={styles.logo}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.logoGradient}
          >
            <Text style={styles.logoText}>W</Text>
          </LinearGradient>
        </View>
        <TouchableOpacity style={styles.notificationButton}>
          <Bell color="#333" size={24} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Featured Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured.</Text>
            <View style={styles.carouselControls}>
              <TouchableOpacity
                style={styles.carouselButton}
                onPress={() => setCurrentFeatured(Math.max(0, currentFeatured - 1))}
              >
                <ChevronLeft color="#666" size={20} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.carouselButton}
                onPress={() => setCurrentFeatured(Math.min(featuredWallpapers.length - 1, currentFeatured + 1))}
              >
                <ChevronRight color="#666" size={20} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.featuredContainer}>
            <Image
              source={{ uri: featuredWallpapers[currentFeatured].url }}
              style={styles.featuredImage}
            />
            <View style={styles.featuredOverlay}>
              <Text style={styles.featuredTitle}>
                {featuredWallpapers[currentFeatured].title}
              </Text>
            </View>
          </View>

          <View style={styles.featuredThumbnails}>
            {featuredWallpapers.slice(0, 3).map((wallpaper, index) => (
              <TouchableOpacity
                key={wallpaper.id}
                style={styles.thumbnail}
                onPress={() => setCurrentFeatured(index)}
              >
                <Image source={{ uri: wallpaper.url }} style={styles.thumbnailImage} />
              </TouchableOpacity>
            ))}
            <View style={styles.moreThumbnail}>
              <Text style={styles.moreThumbnailText}>+4</Text>
            </View>
          </View>
        </View>

        {/* Categories Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <View style={styles.categoriesGrid}>
            {categories.map((category, index) => (
              <TouchableOpacity key={index} style={styles.categoryCard}>
                <LinearGradient
                  colors={[category.color, `${category.color}80`]}
                  style={styles.categoryGradient}
                >
                  <Text style={styles.categoryName}>{category.name}</Text>
                  <Text style={styles.categoryCount}>{category.count} wallpapers</Text>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Trending Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Trending</Text>
            <TouchableOpacity style={styles.viewToggle}>
              <View style={styles.viewToggleIcon} />
              <View style={styles.viewToggleIcon} />
            </TouchableOpacity>
          </View>

          <View style={styles.trendingGrid}>
            {trendingWallpapers.map((url, index) => (
              <TouchableOpacity key={index} style={styles.trendingCard}>
                <Image source={{ uri: url }} style={styles.trendingImage} />
                <TouchableOpacity style={styles.favoriteButton}>
                  <Heart color="#fff" size={16} fill="#fff" />
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    alignItems: 'center',
  },
  logoGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    left: 24,
    marginBottom: 12
  },
  carouselControls: {
    flexDirection: 'row',
    gap: 8,
  },
  carouselButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuredContainer: {
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  featuredImage: {
    width: cardWidth,
    height: 320,
    borderRadius: 16,
  },
  featuredOverlay: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
  },
  featuredTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    left: 24
  },
  featuredThumbnails: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    gap: 12,
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 12,
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  moreThumbnail: {
    width: 60,
    height: 60,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreThumbnailText: {
    color: '#666',
    fontWeight: 'bold',
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 24,
    gap: 12,
  },
  categoryCard: {
    width: (width - 60) / 2,
    height: 100,
    borderRadius: 16,
    overflow: 'hidden',
  },
  categoryGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  categoryName: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  categoryCount: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: '#667eea',
    borderRadius: 8,
    padding: 8,
    gap: 4,
  },
  viewToggleIcon: {
    width: 8,
    height: 8,
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  trendingGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 24,
    gap: 12,
  },
  trendingCard: {
    width: (width - 60) / 2,
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  trendingImage: {
    width: '100%',
    height: '100%',
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});