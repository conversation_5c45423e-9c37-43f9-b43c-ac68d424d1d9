import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Search, Filter, X } from 'lucide-react-native';

const { width } = Dimensions.get('window');

const searchCategories = [
  { name: 'Artistic', count: '1207 Wallpapers', gradient: ['#FF6B6B', '#FF8E8E'] },
  { name: 'Art', count: '643 Wallpapers', gradient: ['#4ECDC4', '#44A08D'] },
  { name: 'Artist', count: '988 Wallpapers', gradient: ['#FA8072', '#FFA07A'] },
  { name: 'Particles', count: '2690 Wallpapers', gradient: ['#1A1A2E', '#16213E'] },
];

const filterTabs = ['Photo', 'Category', 'Author'];

export default function SearchScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('Photo');

  const clearSearch = () => {
    setSearchQuery('');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Search</Text>
        <TouchableOpacity style={styles.closeButton}>
          <X color="#333" size={24} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <Text style={styles.description}>
          searching through hundreds of photos will be so much easier now.
        </Text>

        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Search color="#999" size={20} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search wallpapers..."
              placeholderTextColor="#999"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={clearSearch}>
                <X color="#999" size={20} />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity style={styles.filterButton}>
            <Filter color="#667eea" size={20} />
          </TouchableOpacity>
        </View>

        <View style={styles.filterTabs}>
          {filterTabs.map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.filterTab, activeTab === tab && styles.activeFilterTab]}
              onPress={() => setActiveTab(tab)}
            >
              <Text style={[styles.filterTabText, activeTab === tab && styles.activeFilterTabText]}>
                {tab}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.categoriesContainer}>
            {searchCategories.map((category, index) => (
              <TouchableOpacity key={index} style={styles.categoryCard}>
                <View style={[styles.categoryBackground, { backgroundColor: category.gradient[0] }]}>
                  <Text style={styles.categoryName}>{category.name}</Text>
                  <Text style={styles.categoryCount}>{category.count}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 24,
    lineHeight: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 24,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 2,
    borderColor: '#667eea',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    color: '#333',
  },
  filterButton: {
    width: 44,
    height: 44,
    borderRadius: 12,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#667eea',
  },
  filterTabs: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  filterTab: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    marginRight: 24,
  },
  activeFilterTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#667eea',
  },
  filterTabText: {
    fontSize: 16,
    color: '#666',
  },
  activeFilterTabText: {
    color: '#667eea',
    fontWeight: '600',
  },
  categoriesContainer: {
    gap: 16,
  },
  categoryCard: {
    height: 120,
    borderRadius: 16,
    overflow: 'hidden',
  },
  categoryBackground: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  categoryName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  categoryCount: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
});