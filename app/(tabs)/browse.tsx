import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Grid3x3 as Grid3X3, List, Filter, Heart, Download } from 'lucide-react-native';

const { width } = Dimensions.get('window');

const wallpapers = [
  'https://images.pexels.com/photos/1287145/pexels-photo-1287145.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1323550/pexels-photo-1323550.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1366957/pexels-photo-1366957.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1671324/pexels-photo-1671324.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1624496/pexels-photo-1624496.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1629236/pexels-photo-1629236.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=400',
  'https://images.pexels.com/photos/1366930/pexels-photo-1366930.jpeg?auto=compress&cs=tinysrgb&w=400',
];

export default function BrowseScreen() {
  const [isGridView, setIsGridView] = useState(true);
  const [favorites, setFavorites] = useState<number[]>([]);

  const toggleFavorite = (index: number) => {
    setFavorites(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const renderGridView = () => (
    <View style={styles.gridContainer}>
      {wallpapers.map((url, index) => (
        <TouchableOpacity key={index} style={styles.gridItem}>
          <Image source={{ uri: url }} style={styles.gridImage} />
          <View style={styles.imageOverlay}>
            <TouchableOpacity 
              style={styles.overlayButton}
              onPress={() => toggleFavorite(index)}
            >
              <Heart 
                color="#fff" 
                size={16} 
                fill={favorites.includes(index) ? '#fff' : 'none'}
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.overlayButton}>
              <Download color="#fff" size={16} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderListView = () => (
    <View style={styles.listContainer}>
      {wallpapers.map((url, index) => (
        <TouchableOpacity key={index} style={styles.listItem}>
          <Image source={{ uri: url }} style={styles.listImage} />
          <View style={styles.listContent}>
            <Text style={styles.listTitle}>Wallpaper {index + 1}</Text>
            <Text style={styles.listSubtitle}>High Quality • 4K</Text>
          </View>
          <View style={styles.listActions}>
            <TouchableOpacity 
              style={styles.listActionButton}
              onPress={() => toggleFavorite(index)}
            >
              <Heart 
                color={favorites.includes(index) ? '#667eea' : '#666'} 
                size={20} 
                fill={favorites.includes(index) ? '#667eea' : 'none'}
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.listActionButton}>
              <Download color="#666" size={20} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Browse</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.filterButton}>
            <Filter color="#333" size={20} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.viewToggleButton}
            onPress={() => setIsGridView(!isGridView)}
          >
            {isGridView ? (
              <List color="#333" size={20} />
            ) : (
              <Grid3X3 color="#333" size={20} />
            )}
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {isGridView ? renderGridView() : renderListView()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewToggleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 18,
    gap: 12,
  },
  gridItem: {
    width: (width - 48) / 2,
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  gridImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    top: 12,
    right: 12,
    gap: 8,
  },
  overlayButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingHorizontal: 24,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  listImage: {
    width: 60,
    height: 80,
    borderRadius: 12,
    marginRight: 16,
  },
  listContent: {
    flex: 1,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  listSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  listActions: {
    flexDirection: 'row',
    gap: 12,
  },
  listActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
});