import React, { useEffect } from 'react';
import { router, useRootNavigationState } from 'expo-router';
import { View, StyleSheet } from 'react-native';

export default function Index() {
  const rootNavigationState = useRootNavigationState();

  useEffect(() => {
    if (rootNavigationState?.key) {
      // Check if user is authenticated and onboarded
      // For now, redirect to onboarding
      router.replace('/onboarding');
    }
  }, [rootNavigationState?.key]);

  return <View style={styles.container} />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});