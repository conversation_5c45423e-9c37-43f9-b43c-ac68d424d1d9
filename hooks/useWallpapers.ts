import { useState, useEffect } from 'react';
import { Wallpaper } from '@/types/wallpaper';

const mockWallpapers: Wallpaper[] = [
  {
    id: '1',
    url: 'https://images.pexels.com/photos/1287145/pexels-photo-1287145.jpeg?auto=compress&cs=tinysrgb&w=800',
    thumbnailUrl: 'https://images.pexels.com/photos/1287145/pexels-photo-1287145.jpeg?auto=compress&cs=tinysrgb&w=400',
    title: 'Mountain Landscape',
    category: 'Nature',
    tags: ['mountain', 'landscape', 'nature'],
    resolution: '4K',
    downloads: 1250,
    likes: 320,
    createdAt: '2024-01-15',
  },
  {
    id: '2',
    url: 'https://images.pexels.com/photos/1323550/pexels-photo-1323550.jpeg?auto=compress&cs=tinysrgb&w=800',
    thumbnailUrl: 'https://images.pexels.com/photos/1323550/pexels-photo-1323550.jpeg?auto=compress&cs=tinysrgb&w=400',
    title: 'Abstract Art',
    category: 'Abstract',
    tags: ['abstract', 'art', 'colorful'],
    resolution: 'HD',
    downloads: 890,
    likes: 456,
    createdAt: '2024-01-14',
  },
];

export function useWallpapers() {
  const [wallpapers, setWallpapers] = useState<Wallpaper[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadWallpapers();
  }, []);

  const loadWallpapers = async () => {
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setWallpapers(mockWallpapers);
    } catch (err) {
      setError('Failed to load wallpapers');
    } finally {
      setIsLoading(false);
    }
  };

  const searchWallpapers = async (query: string) => {
    try {
      setIsLoading(true);
      // Simulate search API call
      const filtered = mockWallpapers.filter(w => 
        w.title.toLowerCase().includes(query.toLowerCase()) ||
        w.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      );
      setWallpapers(filtered);
    } catch (err) {
      setError('Search failed');
    } finally {
      setIsLoading(false);
    }
  };

  const getWallpapersByCategory = async (category: string) => {
    try {
      setIsLoading(true);
      const filtered = mockWallpapers.filter(w => w.category === category);
      setWallpapers(filtered);
    } catch (err) {
      setError('Failed to load category wallpapers');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    wallpapers,
    isLoading,
    error,
    loadWallpapers,
    searchWallpapers,
    getWallpapersByCategory,
  };
}