import { useState, useEffect } from 'react';
import { storage, StorageKeys } from '@/utils/storage';
import { User } from '@/types/wallpaper';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      const userData = await storage.getItem<User>(StorageKeys.USER_DATA);
      setUser(userData);
    } catch (error) {
      console.error('Error loading user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      // Simulate API call
      const userData: User = {
        id: '1',
        username: 'john_doe',
        email,
        favorites: [],
        downloads: [],
      };
      
      await storage.setItem(StorageKeys.USER_DATA, userData);
      setUser(userData);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Login failed' };
    }
  };

  const register = async (username: string, email: string, password: string) => {
    try {
      // Simulate API call
      const userData: User = {
        id: '1',
        username,
        email,
        favorites: [],
        downloads: [],
      };
      
      await storage.setItem(StorageKeys.USER_DATA, userData);
      setUser(userData);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Registration failed' };
    }
  };

  const logout = async () => {
    try {
      await storage.removeItem(StorageKeys.USER_DATA);
      setUser(null);
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return {
    user,
    isLoading,
    login,
    register,
    logout,
  };
}